import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

export const PUT: RequestHandler = async ({ params, request, locals: { supabase } }) => {
    try {
        const data = await request.json();

        console.log('Data to update: ', data);
        console.log('Guest ID: ', params.id);

        // Validate required fields
        if (!data.firstName) {
            return json({ error: 'First name is required' }, { status: 400 });
        }

        const { error: updateError } = await supabase
            .from('workforce')
            .update({
                first_name: data.firstName,
                last_name: data.lastName || null,
                email: data.email || null,
                mobile: data.mobile || null,
                whatsapp: data.whatsapp || null,
                tshirt_size: data.tshirtSize || null,
                updated_at: new Date().toISOString()
            })
            .eq('id', params.id);

        if (updateError) {
            console.error('Database update error:', updateError);
            return json({ error: 'Failed to update guest data' }, { status: 500 });
        }

        return json({ success: true });
    } catch (error) {
        console.error('Error in edit-data API:', error);
        return json({ error: 'Internal server error' }, { status: 500 });
    }
};