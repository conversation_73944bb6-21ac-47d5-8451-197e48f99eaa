import { SendMailClient } from 'zeptomail';
import { ZEPTO_TOKEN } from '$env/static/private';
import { formatDate, formatTime } from '$lib/utils/formatdatetime';
import type { Database } from '../../database.types';

const templateKey = '2d6f.44892bd5626c3d76.k1.bd0ef5f0-4d1d-11f0-9b2a-525400d4bb1c.19788b202cf';
const templateAlias = 'yoga';
const fromAddress = '<EMAIL>';
const fromName = 'Yoga Event Guest Relations | eHaris';
const replyToAddress = '<EMAIL>';
const replyToName = 'Yoga Event Guest Relations | eHaris';

type Guest = Database['public']['Tables']['workforce']['Row'];

// export async function sendEmailTemplate(firstName: string, lastName: string, email: string, qrLink: string, eventName: string, startDate: string, endDate: string, startTime: string, endTime: string, location: string, locationUrl: string) {
//     console.log('Send Email Function Started')
//     // console.log('Name: ', name)
//     // console.log('Email: ', email)
//     // console.log('QR Link: ', qrLink)
//     // console.log('Event Name: ', eventName)
//     // console.log('Start Date: ', startDate)
//     // console.log('End Date: ', endDate)
//     // console.log('Start Time: ', startTime)
//     // console.log('End Time: ', endTime)
//     // console.log('Location: ', location)
//     // console.log('Location Url: ', locationUrl)

//     const url = 'api.zeptomail.com/';
//     const token = ZEPTO_TOKEN;

//     const client = new SendMailClient({ url, token });

//     let name = `${firstName} ${lastName}`;

//     try {
//         const resp = await client.sendMailWithTemplate({
//             template_key: templateKey,
//             template_alias: templateAlias,
//             from: {
//                 address: fromAddress,
//                 name: fromName,
//             },
//             to: [
//                 {
//                     email_address: {
//                         address: email,
//                         name: name,
//                     },
//                 },
//             ],
//             merge_info: {
//                 name: name,
//                 qrcode: qrLink,
//                 eventName: eventName,
//                 startDate: formatDate(startDate),
//                 endDate: formatDate(endDate),
//                 startTime: formatTime(startTime),
//                 endTime: formatTime(endTime),
//                 location: location,
//                 locationUrl: locationUrl,
//             },
//             reply_to: [
//                 {
//                     address: replyToAddress,
//                     name: replyToName,
//                 },
//             ],
//         });

//         // console.log('Email sent successfully:', resp);
//         return { success: true, response: resp };
//     } catch (error) {
//         console.error('Error sending email:', error);
//         return { success: false, error };
//     }

// }

/**
 * Send email to a single guest using guest object from database
 */
export async function sendEmailToGuest(
    guest: Guest,
    eventDetails: {
        eventName: string;
        startDate: string;
        endDate: string;
        startTime: string;
        endTime: string;
        location: string;
        locationUrl: string;
    }
) {
    console.log('Guest Details passed to the Send Email Function:', guest);
    console.log('Send Email to Guest Function Started');
    console.log('Guest:', guest.first_name, guest.last_name, guest.email);

    // Validate required fields
    if (!guest.email) {
        console.error('Guest email is missing');
        return { success: false, error: 'Guest email is required' };
    }

    if (!guest.first_name) {
        console.error('Guest first name is missing');
        return { success: false, error: 'Guest first name is required' };
    }

    if (!guest.qr_data) {
        console.error('Guest QR data is missing');
        return { success: false, error: 'Guest QR data is required' };
    }

    const url = 'api.zeptomail.com/';
    const token = ZEPTO_TOKEN;

    const client = new SendMailClient({ url, token });

    const name = guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name;

    try {
        const resp = await client.sendMailWithTemplate({
            template_key: templateKey,
            template_alias: templateAlias,
            from: {
                address: '<EMAIL>',
                name: 'Flare Event Guest Relations | eHaris ',
            },
            to: [
                {
                    email_address: {
                        address: guest.email,
                        name: name,
                    },
                },
            ],
            merge_info: {
                name: name,
                qrcode: guest.qr_code_url || guest.qr_data, // Use QR image URL if available, fallback to QR data
                eventName: eventDetails.eventName,
                startDate: formatDate(eventDetails.startDate),
                endDate: formatDate(eventDetails.endDate),
                startTime: formatTime(eventDetails.startTime),
                endTime: formatTime(eventDetails.endTime),
                location: eventDetails.location,
                locationUrl: eventDetails.locationUrl,
            },
            reply_to: [
                {
                    address: '<EMAIL>',
                    name: 'Flare Event Guest Relations | eHaris',
                },
            ],
        });

        console.log('Email sent successfully to:', guest.email);
        return { success: true, response: resp, guestEmail: guest.email };
    } catch (error) {
        console.error('Error sending email to guest:', guest.email, error);
        return { success: false, error, guestEmail: guest.email };
    }
}

/**
 * Send emails to all guests in the provided array
 */
export async function sendEmailToAllGuests(
    guests: Guest[],
    eventDetails: {
        eventName: string;
        startDate: string;
        endDate: string;
        startTime: string;
        endTime: string;
        location: string;
        locationUrl: string;
    }
) {
    console.log('Send Email to All Guests Function Started');
    console.log('Total guests to email:', guests.length);

    const results = {
        total: guests.length,
        successful: 0,
        failed: 0,
        results: [] as Array<{
            guestEmail: string | null;
            guestName: string;
            success: boolean;
            error?: any;
        }>
    };

    // Filter guests with valid email addresses
    const validGuests = guests.filter(guest =>
        guest.email &&
        guest.first_name &&
        guest.qr_data
    );

    console.log('Valid guests with email:', validGuests.length);
    console.log('Invalid guests (missing email/first_name/qr):', guests.length - validGuests.length);

    // Send emails to each valid guest
    for (const guest of validGuests) {
        try {
            const result = await sendEmailToGuest(guest, eventDetails);

            if (result.success) {
                results.successful++;
                results.results.push({
                    guestEmail: guest.email,
                    guestName: guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name || 'Unknown',
                    success: true
                });
            } else {
                results.failed++;
                results.results.push({
                    guestEmail: guest.email,
                    guestName: guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name || 'Unknown',
                    success: false,
                    error: result.error
                });
            }
        } catch (error) {
            results.failed++;
            results.results.push({
                guestEmail: guest.email,
                guestName: guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name || 'Unknown',
                success: false,
                error: error
            });
            console.error('Unexpected error sending email to guest:', guest.email, error);
        }

        // Add a small delay between emails to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('Email sending completed:');
    console.log('- Total guests:', results.total);
    console.log('- Successful:', results.successful);
    console.log('- Failed:', results.failed);

    return results;
}
