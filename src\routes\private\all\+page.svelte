<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table';
	import { goto } from '$app/navigation';

	let { data }: { data: PageData } = $props();

	// Loading states
	let isSendingAll = $state(false);
	let sendingStates = $state<Record<number, boolean>>({});

	// Event details - you can modify these as needed
	const eventDetails = {
		eventName: 'International Day of Yoga',
		startDate: '2025-06-21',
		endDate: '2025-06-21',
		startTime: '18:00',
		endTime: '20:00',
		location: 'New Ideal Indian School Ground',
		locationUrl: 'https://maps.app.goo.gl/RbAsa2wcG68JW7qp8'
	};

	/**
	 * Send email to a single guest
	 */
	async function sendSingleEmail(guest: any) {
		// Validate guest has required fields
		if (!guest.email) {
			alert('Guest email is missing');
			return;
		}

		if (!guest.first_name) {
			alert('Guest first name is missing');
			return;
		}

		if (!guest.qr_data) {
			alert('Guest QR data is missing');
			return;
		}

		// Set loading state for this specific guest
		sendingStates[guest.id] = true;

		try {
			const response = await fetch('/private/api/send-single-email', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					guest,
					eventDetails
				})
			});

			const result = await response.json();

			if (result.success) {
				const guestName = guest.last_name
					? `${guest.first_name} ${guest.last_name}`
					: guest.first_name;
				alert(`Email sent successfully to ${guestName}`);
				// Optionally refresh the page data to update email status
				window.location.reload();
			} else {
				const guestName = guest.last_name
					? `${guest.first_name} ${guest.last_name}`
					: guest.first_name;
				alert(`Failed to send email to ${guestName}: ${result.error}`);
			}
		} catch (error) {
			console.error('Error sending single email:', error);
			const guestName = guest.last_name
				? `${guest.first_name} ${guest.last_name}`
				: guest.first_name;
			alert(`Error sending email to ${guestName}`);
		} finally {
			sendingStates[guest.id] = false;
		}
	}

	/**
	 * Send emails to all guests
	 */
	async function sendEmailToAll() {
		const confirmed = confirm(
			`Are you sure you want to send emails to all ${data.guests.length} guests? This action cannot be undone.`
		);

		if (!confirmed) return;

		isSendingAll = true;

		try {
			const response = await fetch('/private/api/send-all-emails', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					guests: data.guests,
					eventDetails
				})
			});

			const result = await response.json();

			if (result.success) {
				alert(
					`Email sending completed!\n` +
						`Total: ${result.data.total}\n` +
						`Successful: ${result.data.successful}\n` +
						`Failed: ${result.data.failed}`
				);
				// Optionally refresh the page data to update email statuses
				window.location.reload();
			} else {
				alert(`Failed to send emails: ${result.error}`);
			}
		} catch (error) {
			console.error('Error sending all emails:', error);
			alert('Error sending emails to all guests');
		} finally {
			isSendingAll = false;
		}
	}
</script>

<!-- <div class="container mx-auto p-4"> -->
<div class=" mx-auto p-4">
	<div class="flex flex-col gap-4">
		<div class="flex justify-between items-center">
			<h1 class="text-2xl font-bold">All Badges</h1>
			<Button disabled={isSendingAll} onclick={sendEmailToAll} variant="default">
				{isSendingAll ? 'Sending Emails...' : 'Send Email to All'}
			</Button>
		</div>

		<div class="rounded-lg border">
			<Table.Root>
				<Table.Header class="bg-muted">
					<Table.Row>
						<Table.Head class="border-r">#</Table.Head>

						<Table.Head>First Name</Table.Head>
						<Table.Head>Last Name</Table.Head>
						<Table.Head>Email</Table.Head>
						<Table.Head>WhatsApp 2</Table.Head>
						<Table.Head>QR Data</Table.Head>
						<Table.Head>Category</Table.Head>
						<Table.Head>Email Status</Table.Head>
						<Table.Head>Email Sent At</Table.Head>
						<Table.Head>Actions</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each data.guests as guest, index}
						<Table.Row>
							<Table.Cell class="border-r bg-muted">{index + 1}</Table.Cell>

							<Table.Cell>{guest.first_name}</Table.Cell>
							<Table.Cell>{guest.last_name}</Table.Cell>
							<Table.Cell>{guest.email}</Table.Cell>
							<Table.Cell>{guest.whatsapp}</Table.Cell>
							<Table.Cell>{guest.qr_data}</Table.Cell>
							<Table.Cell>{guest.category}</Table.Cell>
							<Table.Cell>{guest.email_status}</Table.Cell>
							<Table.Cell>{guest.email_sent_at}</Table.Cell>

							<Table.Cell>
								<Button
									size="sm"
									disabled={sendingStates[guest.id] || isSendingAll}
									onclick={() => sendSingleEmail(guest)}
								>
									{sendingStates[guest.id] ? 'Sending...' : 'Send Email'}
								</Button>
								<Button size="sm" onclick={() => goto(`/private/all/edit/${guest.id}`)}>Edit</Button
								>
							</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		</div>
	</div>
</div>
